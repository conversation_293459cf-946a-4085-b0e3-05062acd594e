use solana_stream_sdk::{CommitmentLevel, ShredstreamClient};
use std::env;
use std::fs;
use std::path::Path;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    dotenvy::dotenv().ok();

    let target_shred_count: usize =
        env::var("TARGET_SHRED_COUNT").unwrap_or_else(|_| "1000".to_string()).parse().unwrap_or(1000);

    let output_dir = env::var("OUTPUT_DIR").unwrap_or_else(|_| "tests/data".to_string());

    let endpoint =
        env::var("SHREDSTREAM_ENDPOINT").map_err(|_| "SHREDSTREAM_ENDPOINT environment variable is required")?;

    let target_account = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";

    fs::create_dir_all(&output_dir)?;

    println!("Connecting to endpoint: {}", endpoint);
    let mut client =
        ShredstreamClient::connect(&endpoint).await.map_err(|e| format!("Failed to connect to {}: {}", endpoint, e))?;

    println!("Successfully connected to: {}", endpoint);

    let request = ShredstreamClient::create_entries_request_for_accounts(
        vec![target_account.to_string()],
        vec![],
        vec![],
        Some(CommitmentLevel::Confirmed),
    );

    let mut stream = client.subscribe_entries(request).await?;
    let mut shred_count = 0;

    println!("Starting shred collection...");
    println!("Target: {} shreds", target_shred_count);
    println!("Output directory: {}", output_dir);
    println!("Listening for entries from account: {}", target_account);
    println!("Commitment level: Confirmed");

    while let Some(slot_entry) = stream.message().await? {
        if shred_count >= target_shred_count {
            break;
        }

        shred_count += 1;

        // Serialize the raw bytes directly for cross-platform compatibility
        let raw_data = &slot_entry.entries;

        let filename = format!("shred_{:06}.bin", shred_count);
        let filepath = Path::new(&output_dir).join(&filename);

        fs::write(&filepath, raw_data)?;

        println!(
            "Collected shred {}/{} - Slot: {} - Entries count: {} - Raw size: {} bytes",
            shred_count,
            target_shred_count,
            slot_entry.slot,
            slot_entry.entries.len(),
            raw_data.len()
        );
    }

    println!("Collection completed! {} shreds saved to {}/", shred_count, output_dir);
    Ok(())
}
