# Shredstream Decoder

WASM-compatible Solana Entry decoder with TypeScript-friendly output.

## Features

-   ✅ **100% Binary Compatibility** with official Solana types
-   ✅ **WASM Compatible** - builds for `wasm32-unknown-unknown`
-   ✅ **TypeScript-Friendly** - returns proper TypeScript types
-   ✅ **Minimal Dependencies** - only essential crates

## Development

### Makefile Commands

This project uses Make<PERSON>le for all development tasks. Run `make help` to see available commands:

```bash
# Show all available commands
make help

# Build commands
make build          # Build in debug mode
make build-release  # Build in release mode
make build-wasm     # Build WASM package with TypeScript definitions

# Development commands
make fmt            # Format code with rustfmt
make lint           # Run clippy linter
make clean          # Clean build artifacts

# Testing commands
make test           # Run basic tests
make test-all       # Run comprehensive test suite
make collect-shreds # Collect shred data for testing (requires .env file)
```

### Environment Setup

Create a `.env` file in the project root:

```bash
# Required: Shredstream endpoint URL
SHREDSTREAM_ENDPOINT=https://shreds-far-point.erpc.global

# Optional: Number of shreds to collect (default: 1000)
TARGET_SHRED_COUNT=1000

# Optional: Output directory for collected shreds (default: tests/data)
OUTPUT_DIR=tests/data
```

### Testing Guidelines

This project follows a structured testing approach with clear separation of concerns:

#### Test Directory Structure

```
tests/
├── common/           # Shared testing utilities
│   ├── mock_data.rs     # Mock data generators
│   ├── test_utils.rs    # Testing utility functions
│   ├── fixtures.rs      # Test fixtures and sample data
│   ├── assertions.rs    # Custom assertion helpers
│   └── mod.rs          # Module declarations
├── units/            # Unit tests (mirror src/ structure)
│   ├── lib_test.rs
│   ├── types/
│   │   ├── accounts_test.rs
│   │   ├── entries_test.rs
│   │   ├── instructions_test.rs
│   │   ├── messages_test.rs
│   │   └── transactions_test.rs
│   └── utils/
│       └── message_deserializer_test.rs
└── integrations/     # Integration tests (mirror src/ structure)
    ├── lib_test.rs
    ├── types/
    │   ├── accounts_test.rs
    │   ├── entries_test.rs
    │   ├── instructions_test.rs
    │   ├── messages_test.rs
    │   └── transactions_test.rs
    └── utils/
        └── message_deserializer_test.rs
```

#### Testing Rules

1. **Unit Tests** (`tests/units/`):

    - Test individual functions and methods in isolation
    - Mock external dependencies
    - Focus on single responsibility testing
    - File naming: `[source_file]_test.rs`

2. **Integration Tests** (`tests/integrations/`):

    - Test component interactions and workflows
    - Use real dependencies where possible
    - Test end-to-end functionality
    - File naming: `[source_file]_test.rs`

3. **Common Helpers** (`tests/common/`):

    - Organize helpers by category in separate files
    - `mock_data.rs`: Data generators and mock objects
    - `test_utils.rs`: Utility functions for testing
    - `fixtures.rs`: Static test data and fixtures
    - `assertions.rs`: Custom assertion macros and functions

4. **Directory Structure**:
    - Test directory structure must mirror `src/` exactly
    - Each source file has corresponding unit and integration test files
    - Maintain consistent naming conventions

## Usage

### TypeScript

```typescript
import { decode_entries } from './pkg/shredstream_decoder'

const entries = decode_entries(slot, binaryData)
```

### Build with TypeScript Definitions

```bash
# Build WASM package with TypeScript definitions
make build-wasm
```

The build process automatically generates:

-   `pkg/shredstream_decoder.d.ts` - TypeScript definitions
-   `pkg/shredstream_decoder.js` - JavaScript bindings
-   `pkg/shredstream_decoder_bg.wasm` - WASM binary

## TypeScript Types

The build process automatically generates complete TypeScript definitions:

```typescript
// Function with proper return type
function decode_entries(slot: bigint, bytes: Uint8Array): ParsedEntry

// Generated interfaces
interface ParsedEntry {
    slot: number
    entries: Entry[]
}

interface Entry {
    num_hashes: number
    hash: Hash
    transactions: VersionedTransaction[]
}

// All nested types are also generated automatically
interface VersionedTransaction {
    signatures: Signature[]
    message: VersionedMessage
}

// And many more...
```

Usage:

```typescript
import { decode_entries, ParsedEntry, Entry } from './pkg/shredstream_decoder'

const result: ParsedEntry = decode_entries(slot, binaryData)
const entries: Entry[] = result.entries
console.log('Slot:', result.slot)
```
