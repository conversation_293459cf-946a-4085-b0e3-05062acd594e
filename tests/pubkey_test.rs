mod common;

use shredstream_decoder::types::Pubkey;
use solana_pubkey::Pubkey as SolanaPubkey;

use common::{
    assertions::*,
    reference_implementations::reference::*,
};

#[test]
fn test_pubkey_basic_compatibility() {
    let test_array = [42u8; 32];
    let custom_pubkey = Pubkey::new_from_array(test_array);
    let reference_pubkey = SolanaPubkey::new_from_array(test_array);

    assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
    assert_eq!(custom_pubkey.to_bytes(), reference_pubkey.to_bytes());
    
    let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
    let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
    
    assert_binary_compatible!(custom_serialized, reference_serialized);
}

#[test]
fn test_pubkey_zero_bytes() {
    let zero_array = [0u8; 32];
    let custom_pubkey = Pubkey::new_from_array(zero_array);
    let reference_pubkey = SolanaPubkey::new_from_array(zero_array);

    assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
    assert_eq!(custom_pubkey.to_bytes(), zero_array);
    assert_eq!(reference_pubkey.to_bytes(), zero_array);

    let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
    let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
    assert_binary_compatible!(custom_serialized, reference_serialized);

    assert_roundtrip_compatible!(custom_pubkey);
}

#[test]
fn test_pubkey_max_bytes() {
    let max_array = [255u8; 32];
    let custom_pubkey = Pubkey::new_from_array(max_array);
    let reference_pubkey = SolanaPubkey::new_from_array(max_array);

    assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
    assert_eq!(custom_pubkey.to_bytes(), max_array);
    assert_eq!(reference_pubkey.to_bytes(), max_array);

    let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
    let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
    assert_binary_compatible!(custom_serialized, reference_serialized);

    assert_roundtrip_compatible!(custom_pubkey);
}

#[test]
fn test_pubkey_from_array_compatibility() {
    let test_arrays = vec![
        [0u8; 32],
        [1u8; 32],
        [255u8; 32],
        [42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57,
         58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73],
    ];

    for test_array in test_arrays {
        let custom_pubkey = Pubkey::from(test_array);
        let reference_pubkey = SolanaPubkey::from(test_array);

        assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
        assert_eq!(custom_pubkey.to_bytes(), reference_pubkey.to_bytes());
        
        let custom_pubkey2 = Pubkey::new_from_array(test_array);
        assert_eq!(custom_pubkey.as_ref(), custom_pubkey2.as_ref());
    }
}

#[test]
fn test_pubkey_serialization_roundtrip() {
    let test_cases = vec![
        [0u8; 32],
        [255u8; 32],
        [128u8; 32],
        [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
         17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32],
    ];

    for test_array in test_cases {
        let original_pubkey = Pubkey::new_from_array(test_array);
        
        let serialized = bincode::serialize(&original_pubkey).unwrap();
        let deserialized: Pubkey = bincode::deserialize(&serialized).unwrap();
        
        assert_eq!(original_pubkey, deserialized);
        assert_eq!(original_pubkey.as_ref(), deserialized.as_ref());
        assert_eq!(original_pubkey.to_bytes(), deserialized.to_bytes());
        
        let re_serialized = bincode::serialize(&deserialized).unwrap();
        assert_eq!(serialized, re_serialized);
    }
}
