mod common;

use shredstream_decoder::types::VersionedTransaction;

use common::{
    assertions::*,
    compatibility_validators::validators::*,
    fixtures::*,
    mock_data::generators::*,
    reference_implementations::reference::*,
};

mod versioned_transaction_tests {
    use super::*;

    #[test]
    fn test_versioned_transaction_placeholder() {
        // TODO: Implement VersionedTransaction tests
        // This is a Level 3 struct depending on VersionedMessage
        assert!(true);
    }
}
