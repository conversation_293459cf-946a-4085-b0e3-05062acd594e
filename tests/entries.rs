mod common;

use shredstream_decoder::types::{Entry, ParsedEntry};

use common::{
    assertions::*,
    compatibility_validators::validators::*,
    fixtures::*,
    mock_data::generators::*,
    reference_implementations::reference::*,
};

mod entry_tests {
    use super::*;

    #[test]
    fn test_entry_placeholder() {
        // TODO: Implement Entry tests
        // This is a Level 4 struct depending on VersionedTransaction
        assert!(true);
    }
}

mod parsed_entry_tests {
    use super::*;

    #[test]
    fn test_parsed_entry_placeholder() {
        // TODO: Implement ParsedEntry tests
        // This is a Level 4 struct depending on Entry
        assert!(true);
    }
}
