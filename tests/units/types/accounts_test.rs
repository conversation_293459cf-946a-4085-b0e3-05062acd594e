use shredstream_decoder::types::{MessageAddressTableLookup, Pubkey};
use solana_pubkey::Pubkey as SolanaPubkey;

use crate::common::{
    assertions::*, compatibility_validators::validators::*, fixtures::*, mock_data::generators::*,
    reference_implementations::reference::*,
};

mod pubkey_tests {
    use super::*;

    #[test]
    fn test_pubkey_binary_representation() {
        let test_cases = vec![
            [0u8; 32],
            [255u8; 32],
            [
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
                29, 30, 31, 32,
            ],
        ];

        for test_array in test_cases {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
            assert_eq!(custom_pubkey.to_bytes(), test_array);
            assert_eq!(reference_pubkey.to_bytes(), test_array);

            assert_pubkey_compatible!(custom_pubkey, reference_pubkey);
        }
    }

    #[test]
    fn test_pubkey_serialization_compatibility() {
        let test_arrays = vec![
            [0u8; 32],
            [255u8; 32],
            get_zero_pubkey_bytes().try_into().unwrap(),
            get_max_pubkey_bytes().try_into().unwrap(),
        ];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
            let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();

            assert_binary_compatible!(custom_serialized, reference_serialized);
            assert_serialization_compatible!(custom_pubkey, reference_pubkey);
        }
    }

    #[test]
    fn test_pubkey_deserialization_compatibility() {
        let test_arrays = vec![[0u8; 32], [255u8; 32], [42u8; 32]];

        for test_array in test_arrays {
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);
            let reference_bytes = serialize_pubkey_reference(&reference_pubkey).unwrap();

            let custom_result: Result<Pubkey, _> = bincode::deserialize(&reference_bytes);
            let reference_result = deserialize_pubkey_reference(&reference_bytes);

            match (custom_result, reference_result) {
                (Ok(custom), Ok(reference)) => {
                    assert_pubkey_compatible!(custom, reference);
                    assert_eq!(custom.to_bytes(), reference.to_bytes());
                }
                (Err(_), Err(_)) => {}
                _ => panic!("Deserialization compatibility failed: one succeeded, one failed"),
            }
        }
    }

    #[test]
    fn test_pubkey_from_array_compatibility() {
        let test_arrays = vec![
            [0u8; 32],
            [1u8; 32],
            [255u8; 32],
            [
                42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67,
                68, 69, 70, 71, 72, 73,
            ],
        ];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::from(test_array);
            let reference_pubkey = SolanaPubkey::from(test_array);

            assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
            assert_eq!(custom_pubkey.to_bytes(), reference_pubkey.to_bytes());

            let custom_pubkey2 = Pubkey::new_from_array(test_array);
            assert_eq!(custom_pubkey.as_ref(), custom_pubkey2.as_ref());
        }
    }

    #[test]
    fn test_pubkey_to_bytes_compatibility() {
        let test_arrays = vec![[0u8; 32], [255u8; 32], [128u8; 32]];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            let custom_bytes = custom_pubkey.to_bytes();
            let reference_bytes = reference_pubkey.to_bytes();

            assert_eq!(custom_bytes, reference_bytes);
            assert_eq!(custom_bytes, test_array);
            assert_eq!(reference_bytes, test_array);
        }
    }

    #[test]
    fn test_pubkey_as_ref_compatibility() {
        let test_arrays = vec![
            [0u8; 32],
            [255u8; 32],
            [
                1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28,
                29, 30, 31, 32,
            ],
        ];

        for test_array in test_arrays {
            let custom_pubkey = Pubkey::new_from_array(test_array);
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            let custom_ref: &[u8] = custom_pubkey.as_ref();
            let reference_ref: &[u8] = reference_pubkey.as_ref();

            assert_eq!(custom_ref, reference_ref);
            assert_eq!(custom_ref, &test_array[..]);
            assert_eq!(reference_ref, &test_array[..]);
            assert_eq!(custom_ref.len(), 32);
            assert_eq!(reference_ref.len(), 32);
        }
    }

    #[test]
    fn test_pubkey_zero_bytes() {
        let zero_array = [0u8; 32];
        let custom_pubkey = Pubkey::new_from_array(zero_array);
        let reference_pubkey = SolanaPubkey::new_from_array(zero_array);

        assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
        assert_eq!(custom_pubkey.to_bytes(), zero_array);
        assert_eq!(reference_pubkey.to_bytes(), zero_array);

        let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
        let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
        assert_binary_compatible!(custom_serialized, reference_serialized);

        assert_roundtrip_compatible!(custom_pubkey);
    }

    #[test]
    fn test_pubkey_max_bytes() {
        let max_array = [255u8; 32];
        let custom_pubkey = Pubkey::new_from_array(max_array);
        let reference_pubkey = SolanaPubkey::new_from_array(max_array);

        assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
        assert_eq!(custom_pubkey.to_bytes(), max_array);
        assert_eq!(reference_pubkey.to_bytes(), max_array);

        let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
        let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
        assert_binary_compatible!(custom_serialized, reference_serialized);

        assert_roundtrip_compatible!(custom_pubkey);
    }

    #[test]
    fn test_pubkey_random_bytes() {
        let test_seeds = vec![12345u64, 67890, 999999, 1, u64::MAX];

        for seed in test_seeds {
            let custom_pubkey = generate_mock_pubkey_with_seed(seed);
            let test_array = custom_pubkey.to_bytes();
            let reference_pubkey = SolanaPubkey::new_from_array(test_array);

            assert_eq!(custom_pubkey.as_ref(), reference_pubkey.as_ref());
            assert_eq!(custom_pubkey.to_bytes(), reference_pubkey.to_bytes());

            let custom_serialized = bincode::serialize(&custom_pubkey).unwrap();
            let reference_serialized = serialize_pubkey_reference(&reference_pubkey).unwrap();
            assert_binary_compatible!(custom_serialized, reference_serialized);

            assert_roundtrip_compatible!(custom_pubkey);
        }
    }

    #[test]
    fn test_pubkey_default_compatibility() {
        let custom_default = Pubkey::default();
        let reference_default = SolanaPubkey::default();

        assert_eq!(custom_default.as_ref(), reference_default.as_ref());
        assert_eq!(custom_default.to_bytes(), reference_default.to_bytes());
        assert_eq!(custom_default.to_bytes(), [0u8; 32]);

        let custom_serialized = bincode::serialize(&custom_default).unwrap();
        let reference_serialized = serialize_pubkey_reference(&reference_default).unwrap();
        assert_binary_compatible!(custom_serialized, reference_serialized);
    }

    #[test]
    fn test_pubkey_clone_and_copy() {
        let test_array = [42u8; 32];
        let original = Pubkey::new_from_array(test_array);

        let cloned = original.clone();
        let copied = original;

        assert_eq!(original.as_ref(), cloned.as_ref());
        assert_eq!(original.as_ref(), copied.as_ref());
        assert_eq!(cloned.as_ref(), copied.as_ref());

        assert_eq!(original.to_bytes(), test_array);
        assert_eq!(cloned.to_bytes(), test_array);
        assert_eq!(copied.to_bytes(), test_array);
    }

    #[test]
    fn test_pubkey_equality_and_ordering() {
        let array1 = [1u8; 32];
        let array2 = [2u8; 32];
        let array1_copy = [1u8; 32];

        let pubkey1 = Pubkey::new_from_array(array1);
        let pubkey2 = Pubkey::new_from_array(array2);
        let pubkey1_copy = Pubkey::new_from_array(array1_copy);

        assert_eq!(pubkey1, pubkey1_copy);
        assert_ne!(pubkey1, pubkey2);
        assert!(pubkey1 < pubkey2);
        assert!(pubkey1 <= pubkey1_copy);
        assert!(pubkey2 > pubkey1);
    }

    #[test]
    fn test_pubkey_hash_compatibility() {
        use std::collections::HashMap;

        let test_array = [123u8; 32];
        let custom_pubkey = Pubkey::new_from_array(test_array);

        let mut map = HashMap::new();
        map.insert(custom_pubkey, "test_value");

        let lookup_key = Pubkey::new_from_array(test_array);
        assert_eq!(map.get(&lookup_key), Some(&"test_value"));

        let different_key = Pubkey::new_from_array([124u8; 32]);
        assert_eq!(map.get(&different_key), None);
    }

    #[test]
    fn test_pubkey_comprehensive_roundtrip() {
        let test_cases = vec![
            [0u8; 32],
            [255u8; 32],
            [128u8; 32],
            generate_mock_pubkey_with_seed(12345).to_bytes(),
            generate_mock_pubkey_with_seed(67890).to_bytes(),
        ];

        for test_array in test_cases {
            let original_pubkey = Pubkey::new_from_array(test_array);

            let serialized = bincode::serialize(&original_pubkey).unwrap();
            let deserialized: Pubkey = bincode::deserialize(&serialized).unwrap();

            assert_eq!(original_pubkey, deserialized);
            assert_eq!(original_pubkey.as_ref(), deserialized.as_ref());
            assert_eq!(original_pubkey.to_bytes(), deserialized.to_bytes());

            let re_serialized = bincode::serialize(&deserialized).unwrap();
            assert_eq!(serialized, re_serialized);
        }
    }
}
