mod common;

use shredstream_decoder::types::CompiledInstruction;

use common::{assertions::*, mock_data::generators::*};

mod compiled_instruction_tests {
    use super::*;

    #[test]
    fn test_compiled_instruction_basic_structure() {
        let custom_instruction =
            CompiledInstruction { program_id_index: 42, accounts: vec![0, 1, 2], data: vec![1, 2, 3, 4] };

        assert_eq!(custom_instruction.program_id_index, 42);
        assert_eq!(custom_instruction.accounts, vec![0, 1, 2]);
        assert_eq!(custom_instruction.data, vec![1, 2, 3, 4]);

        assert_roundtrip_compatible!(custom_instruction);
    }

    #[test]
    fn test_compiled_instruction_program_id_index_values() {
        let test_indices = vec![0u8, 1, 42, 128, 255];

        for index in test_indices {
            let custom_instruction = CompiledInstruction {
                program_id_index: index,
                accounts: vec![],
                data: vec![],
            };

            assert_eq!(custom_instruction.program_id_index, index);
            assert_eq!(custom_instruction.accounts.len(), 0);
            assert_eq!(custom_instruction.data.len(), 0);

            assert_roundtrip_compatible!(custom_instruction);
        }
    }

    #[test]
    fn test_compiled_instruction_accounts_array() {
        let test_cases = vec![
            vec![],
            vec![0],
            vec![0, 1, 2],
            vec![255, 254, 253],
            (0..10).collect::<Vec<u8>>(),
            (0..100).map(|i| (i % 256) as u8).collect(),
        ];

        for accounts in test_cases {
            let custom_instruction = CompiledInstruction {
                program_id_index: 0,
                accounts: accounts.clone(),
                data: vec![],
            };

            assert_eq!(custom_instruction.accounts, accounts);
            assert_eq!(custom_instruction.accounts.len(), accounts.len());

            assert_roundtrip_compatible!(custom_instruction);
        }
    }

    #[test]
    fn test_compiled_instruction_data_array() {
        let test_cases = vec![
            vec![],
            vec![42],
            vec![1, 2, 3, 4],
            vec![255, 0, 128, 64],
            (0..20).collect::<Vec<u8>>(),
            (0..200).map(|i| (i % 256) as u8).collect(),
        ];

        for data in test_cases {
            let custom_instruction = CompiledInstruction {
                program_id_index: 1,
                accounts: vec![0],
                data: data.clone(),
            };

            assert_eq!(custom_instruction.data, data);
            assert_eq!(custom_instruction.data.len(), data.len());

            assert_roundtrip_compatible!(custom_instruction);
        }
    }

    #[test]
    fn test_compiled_instruction_serialization_roundtrip() {
        let test_instructions = vec![
            CompiledInstruction { program_id_index: 0, accounts: vec![], data: vec![] },
            CompiledInstruction { program_id_index: 255, accounts: vec![255], data: vec![255] },
            generate_mock_compiled_instruction(),
            generate_mock_compiled_instruction_with_seed(12345),
            generate_mock_compiled_instruction_with_seed(67890),
        ];

        for instruction in test_instructions {
            let serialized = bincode::serialize(&instruction).unwrap();
            let deserialized: CompiledInstruction = bincode::deserialize(&serialized).unwrap();

            assert_eq!(instruction, deserialized);
            assert_eq!(instruction.program_id_index, deserialized.program_id_index);
            assert_eq!(instruction.accounts, deserialized.accounts);
            assert_eq!(instruction.data, deserialized.data);

            let re_serialized = bincode::serialize(&deserialized).unwrap();
            assert_eq!(serialized, re_serialized);

            assert_roundtrip_compatible!(instruction);
        }
    }

    #[test]
    fn test_compiled_instruction_default() {
        let custom_default = CompiledInstruction::default();

        assert_eq!(custom_default.program_id_index, 0);
        assert_eq!(custom_default.accounts, Vec::<u8>::new());
        assert_eq!(custom_default.data, Vec::<u8>::new());

        assert_roundtrip_compatible!(custom_default);
    }

    #[test]
    fn test_compiled_instruction_clone_and_equality() {
        let original = CompiledInstruction { program_id_index: 42, accounts: vec![0, 1, 2], data: vec![1, 2, 3, 4] };

        let cloned = original.clone();
        let copied = original.clone();

        assert_eq!(original, cloned);
        assert_eq!(original, copied);
        assert_eq!(cloned, copied);

        assert_eq!(original.program_id_index, cloned.program_id_index);
        assert_eq!(original.accounts, cloned.accounts);
        assert_eq!(original.data, cloned.data);
    }

    #[test]
    fn test_compiled_instruction_with_mock_generators() {
        let test_seeds = vec![0u64, 1, 42, 12345, 67890, u64::MAX];

        for seed in test_seeds {
            let custom_instruction = generate_mock_compiled_instruction_with_seed(seed);

            assert_eq!(custom_instruction.program_id_index, (seed % 256) as u8);
            assert_eq!(custom_instruction.accounts.len(), 2);
            assert_eq!(custom_instruction.data.len(), (seed % 10 + 1) as usize);

            assert_roundtrip_compatible!(custom_instruction);
        }
    }

    #[test]
    fn test_compiled_instruction_edge_cases() {
        let edge_cases = vec![
            (0u8, vec![], vec![]),
            (255u8, vec![255], vec![255]),
            (128u8, vec![0, 128, 255], vec![0, 128, 255]),
            (1u8, vec![0], vec![]),
            (254u8, vec![], vec![0]),
        ];

        for (program_id_index, accounts, data) in edge_cases {
            let custom_instruction = CompiledInstruction {
                program_id_index,
                accounts: accounts.clone(),
                data: data.clone(),
            };

            assert_eq!(custom_instruction.program_id_index, program_id_index);
            assert_eq!(custom_instruction.accounts, accounts);
            assert_eq!(custom_instruction.data, data);

            assert_roundtrip_compatible!(custom_instruction);
        }
    }

    #[test]
    fn test_compiled_instruction_large_arrays() {
        let large_accounts: Vec<u8> = (0..255).collect();
        let large_data: Vec<u8> = (0..1000).map(|i| (i % 256) as u8).collect();

        let custom_instruction = CompiledInstruction {
            program_id_index: 128,
            accounts: large_accounts.clone(),
            data: large_data.clone(),
        };

        assert_eq!(custom_instruction.accounts.len(), 255);
        assert_eq!(custom_instruction.data.len(), 1000);

        assert_roundtrip_compatible!(custom_instruction);
    }

    #[test]
    fn test_compiled_instruction_comprehensive_roundtrip() {
        let comprehensive_cases = vec![
            CompiledInstruction::default(),
            generate_mock_compiled_instruction(),
            CompiledInstruction { program_id_index: 255, accounts: vec![255; 255], data: vec![255; 255] },
            CompiledInstruction { program_id_index: 0, accounts: vec![0; 100], data: vec![0; 100] },
            generate_mock_compiled_instruction_with_seed(999999),
        ];

        for instruction in comprehensive_cases {
            let serialized = bincode::serialize(&instruction).unwrap();
            let deserialized: CompiledInstruction = bincode::deserialize(&serialized).unwrap();

            assert_eq!(instruction, deserialized);
            assert_eq!(instruction.program_id_index, deserialized.program_id_index);
            assert_eq!(instruction.accounts, deserialized.accounts);
            assert_eq!(instruction.data, deserialized.data);

            let re_serialized = bincode::serialize(&deserialized).unwrap();
            assert_eq!(serialized, re_serialized);
        }
    }
}
