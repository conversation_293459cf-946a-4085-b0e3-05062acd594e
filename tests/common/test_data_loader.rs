use std::path::{Path, PathBuf};
use std::fs;
use std::io;

pub mod data_loader {
    use super::*;

    #[derive(<PERSON>bug, <PERSON><PERSON>)]
    pub struct ShredDataset {
        pub files: Vec<ShredFile>,
        pub total_samples: usize,
    }

    #[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
    pub struct ShredFile {
        pub path: PathBuf,
        pub slot: u64,
        pub size: usize,
        pub data: Vec<u8>,
    }

    #[derive(Debu<PERSON>, <PERSON>lone)]
    pub enum DataCategory {
        Small,    // < 1KB
        Medium,   // 1KB - 10KB  
        Large,    // > 10KB
        EdgeCase, // Special patterns
    }

    impl ShredDataset {
        pub fn new() -> Self {
            Self {
                files: Vec::new(),
                total_samples: 0,
            }
        }

        pub fn add_file(&mut self, file: ShredFile) {
            self.total_samples += 1;
            self.files.push(file);
        }

        pub fn get_by_category(&self, category: DataCategory) -> Vec<&ShredFile> {
            self.files.iter().filter(|file| {
                match category {
                    DataCategory::Small => file.size < 1024,
                    DataCategory::Medium => file.size >= 1024 && file.size <= 10240,
                    DataCategory::Large => file.size > 10240,
                    DataCategory::EdgeCase => file.size == 0 || file.size > 50000,
                }
            }).collect()
        }

        pub fn get_random_sample(&self, count: usize) -> Vec<&ShredFile> {
            use std::collections::HashSet;
            
            if count >= self.files.len() {
                return self.files.iter().collect();
            }

            let mut selected = HashSet::new();
            let mut result = Vec::new();
            
            while result.len() < count && selected.len() < self.files.len() {
                let index = (result.len() * 7 + 13) % self.files.len(); // Simple pseudo-random
                if selected.insert(index) {
                    result.push(&self.files[index]);
                }
            }
            
            result
        }
    }

    impl ShredFile {
        pub fn new(path: PathBuf, slot: u64, data: Vec<u8>) -> Self {
            let size = data.len();
            Self { path, slot, size, data }
        }

        pub fn from_path(path: &Path) -> Result<Self, io::Error> {
            let data = fs::read(path)?;
            let slot = extract_slot_from_filename(path).unwrap_or(0);
            Ok(Self::new(path.to_path_buf(), slot, data))
        }

        pub fn get_category(&self) -> DataCategory {
            match self.size {
                0..=1023 => DataCategory::Small,
                1024..=10240 => DataCategory::Medium,
                10241..=50000 => DataCategory::Large,
                _ => DataCategory::EdgeCase,
            }
        }
    }

    pub fn load_shred_dataset() -> Result<ShredDataset, io::Error> {
        let data_dir = Path::new("tests/data/raw_shreds");
        load_shred_dataset_from_dir(data_dir)
    }

    pub fn load_shred_dataset_from_dir(dir: &Path) -> Result<ShredDataset, io::Error> {
        let mut dataset = ShredDataset::new();

        if !dir.exists() {
            return Ok(dataset);
        }

        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() && is_shred_file(&path) {
                match ShredFile::from_path(&path) {
                    Ok(shred_file) => dataset.add_file(shred_file),
                    Err(e) => eprintln!("Failed to load shred file {:?}: {}", path, e),
                }
            }
        }

        Ok(dataset)
    }

    pub fn load_shred_file(path: &Path) -> Result<ShredFile, io::Error> {
        ShredFile::from_path(path)
    }

    pub fn get_test_samples(dataset: &ShredDataset, sample_size: usize) -> Vec<&ShredFile> {
        if sample_size == 0 {
            return Vec::new();
        }

        let small_count = sample_size / 4;
        let medium_count = sample_size / 2;
        let large_count = sample_size / 4;

        let mut samples = Vec::new();
        
        let small_files = dataset.get_by_category(DataCategory::Small);
        samples.extend(small_files.into_iter().take(small_count));
        
        let medium_files = dataset.get_by_category(DataCategory::Medium);
        samples.extend(medium_files.into_iter().take(medium_count));
        
        let large_files = dataset.get_by_category(DataCategory::Large);
        samples.extend(large_files.into_iter().take(large_count));

        if samples.len() < sample_size {
            let remaining = sample_size - samples.len();
            let random_samples = dataset.get_random_sample(remaining);
            samples.extend(random_samples);
        }

        samples.truncate(sample_size);
        samples
    }

    pub fn get_edge_case_samples(dataset: &ShredDataset) -> Vec<&ShredFile> {
        let mut edge_cases = Vec::new();
        
        if let Some(smallest) = dataset.files.iter().min_by_key(|f| f.size) {
            edge_cases.push(smallest);
        }
        
        if let Some(largest) = dataset.files.iter().max_by_key(|f| f.size) {
            edge_cases.push(largest);
        }
        
        edge_cases.extend(dataset.get_by_category(DataCategory::EdgeCase));
        
        edge_cases.sort_by_key(|f| f.size);
        edge_cases.dedup_by_key(|f| &f.path);
        
        edge_cases
    }

    pub fn create_test_data_directory() -> Result<(), io::Error> {
        let base_dir = Path::new("tests/data");
        fs::create_dir_all(base_dir.join("raw_shreds"))?;
        fs::create_dir_all(base_dir.join("processed/small_entries"))?;
        fs::create_dir_all(base_dir.join("processed/medium_entries"))?;
        fs::create_dir_all(base_dir.join("processed/large_entries"))?;
        fs::create_dir_all(base_dir.join("processed/edge_cases"))?;
        fs::create_dir_all(base_dir.join("reference_outputs/entries"))?;
        fs::create_dir_all(base_dir.join("reference_outputs/transactions"))?;
        fs::create_dir_all(base_dir.join("reference_outputs/messages"))?;
        Ok(())
    }

    pub fn save_processed_data(dataset: &ShredDataset) -> Result<(), io::Error> {
        create_test_data_directory()?;
        
        let base_dir = Path::new("tests/data/processed");
        
        for file in &dataset.files {
            let target_dir = match file.get_category() {
                DataCategory::Small => base_dir.join("small_entries"),
                DataCategory::Medium => base_dir.join("medium_entries"),
                DataCategory::Large => base_dir.join("large_entries"),
                DataCategory::EdgeCase => base_dir.join("edge_cases"),
            };
            
            let target_path = target_dir.join(file.path.file_name().unwrap());
            fs::write(target_path, &file.data)?;
        }
        
        Ok(())
    }

    fn is_shred_file(path: &Path) -> bool {
        if let Some(extension) = path.extension() {
            extension == "bin" || extension == "dat"
        } else {
            path.file_name()
                .and_then(|name| name.to_str())
                .map(|name| name.starts_with("shred_"))
                .unwrap_or(false)
        }
    }

    fn extract_slot_from_filename(path: &Path) -> Option<u64> {
        path.file_stem()
            .and_then(|stem| stem.to_str())
            .and_then(|name| {
                if name.starts_with("shred_") {
                    name[6..].parse().ok()
                } else {
                    None
                }
            })
    }

    pub fn get_dataset_statistics(dataset: &ShredDataset) -> String {
        let small_count = dataset.get_by_category(DataCategory::Small).len();
        let medium_count = dataset.get_by_category(DataCategory::Medium).len();
        let large_count = dataset.get_by_category(DataCategory::Large).len();
        let edge_count = dataset.get_by_category(DataCategory::EdgeCase).len();
        
        let total_size: usize = dataset.files.iter().map(|f| f.size).sum();
        let avg_size = if dataset.total_samples > 0 { total_size / dataset.total_samples } else { 0 };
        
        format!(
            "Dataset Statistics:\n\
             Total files: {}\n\
             Small files (< 1KB): {}\n\
             Medium files (1KB-10KB): {}\n\
             Large files (> 10KB): {}\n\
             Edge case files: {}\n\
             Total size: {} bytes\n\
             Average size: {} bytes",
            dataset.total_samples, small_count, medium_count, large_count, edge_count, total_size, avg_size
        )
    }
}
