use super::test_data_loader::data_loader::{load_shred_dataset, ShredDataset};
use std::sync::OnceLock;

pub mod fixtures {
    use super::*;

    static SHRED_DATASET: OnceLock<ShredDataset> = OnceLock::new();
    static SAMPLE_ENTRIES: OnceLock<Vec<Vec<u8>>> = OnceLock::new();
    static EDGE_CASE_DATA: OnceLock<Vec<Vec<u8>>> = OnceLock::new();

    pub fn get_shred_dataset() -> &'static ShredDataset {
        SHRED_DATASET.get_or_init(|| load_shred_dataset().unwrap_or_else(|_| ShredDataset::new()))
    }

    pub fn get_sample_entries() -> &'static Vec<Vec<u8>> {
        SAMPLE_ENTRIES.get_or_init(|| load_sample_entries())
    }

    pub fn get_edge_case_data() -> &'static Vec<Vec<u8>> {
        ED<PERSON>_CASE_DATA.get_or_init(|| load_edge_case_data())
    }

    pub fn get_known_good_entry_bytes() -> &'static [u8] {
        &[
            1, 0, 0, 0, 0, 0, 0, 0, // num_hashes: 1
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // hash (32 bytes of zeros)
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // transactions length: 0
        ]
    }

    pub fn get_known_good_transaction_bytes() -> &'static [u8] {
        &[
            1, 0, 0, 0, // signatures length: 1
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // signature (64 bytes of zeros)
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, // message version (Legacy)
        ]
    }

    pub fn get_known_good_message_bytes() -> &'static [u8] {
        &[
            255, // Legacy message version
            1, 0, 0, // header: 1 required signature, 0 readonly signed, 0 readonly unsigned
            1, 0, 0, 0, // account_keys length: 1
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // pubkey (32 bytes of zeros)
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
            0, // recent_blockhash (32 bytes of zeros)
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // instructions length: 0
        ]
    }

    pub fn get_small_entries() -> Vec<&'static [u8]> {
        vec![
            get_known_good_entry_bytes(),
            &[
                0, 0, 0, 0, 0, 0, 0, 0, // num_hashes: 0
                1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, // hash
                1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, // transactions length: 0
            ],
        ]
    }

    pub fn get_large_entries() -> Vec<&'static [u8]> {
        vec![]
    }

    pub fn get_complex_entries() -> Vec<&'static [u8]> {
        vec![]
    }

    pub fn get_empty_entry_bytes() -> &'static [u8] {
        &[
            0, 0, 0, 0, 0, 0, 0, 0, // num_hashes: 0
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // hash (32 bytes of zeros)
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, // transactions length: 0
        ]
    }

    pub fn get_single_transaction_entry_bytes() -> &'static [u8] {
        &[
            1, 0, 0, 0, 0, 0, 0, 0, // num_hashes: 1
            2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, // hash
            2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 0, 0, 0, // transactions length: 1
        ]
    }

    pub fn get_zero_hash_bytes() -> &'static [u8] {
        &[0; 32]
    }

    pub fn get_max_hash_bytes() -> &'static [u8] {
        &[255; 32]
    }

    pub fn get_random_hash_bytes() -> &'static [u8] {
        &[
            0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0xaa, 0xbb,
            0xcc, 0xdd, 0xee, 0xff, 0x00, 0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88, 0x99,
        ]
    }

    pub fn get_zero_signature_bytes() -> &'static [u8] {
        &[0; 64]
    }

    pub fn get_max_signature_bytes() -> &'static [u8] {
        &[255; 64]
    }

    pub fn get_zero_pubkey_bytes() -> &'static [u8] {
        &[0; 32]
    }

    pub fn get_max_pubkey_bytes() -> &'static [u8] {
        &[255; 32]
    }

    fn load_sample_entries() -> Vec<Vec<u8>> {
        let mut entries = Vec::new();

        entries.push(get_known_good_entry_bytes().to_vec());
        entries.push(get_empty_entry_bytes().to_vec());
        entries.push(get_single_transaction_entry_bytes().to_vec());

        let dataset = get_shred_dataset();
        if !dataset.files.is_empty() {
            let samples = crate::common::test_data_loader::data_loader::get_test_samples(dataset, 10);
            for sample in samples {
                entries.push(sample.data.clone());
            }
        }

        entries
    }

    fn load_edge_case_data() -> Vec<Vec<u8>> {
        let mut edge_cases = Vec::new();

        edge_cases.push(vec![]);
        edge_cases.push(vec![0]);
        edge_cases.push(vec![255]);
        edge_cases.push(get_zero_hash_bytes().to_vec());
        edge_cases.push(get_max_hash_bytes().to_vec());
        edge_cases.push(get_zero_signature_bytes().to_vec());
        edge_cases.push(get_max_signature_bytes().to_vec());

        let dataset = get_shred_dataset();
        if !dataset.files.is_empty() {
            let edge_samples = crate::common::test_data_loader::data_loader::get_edge_case_samples(dataset);
            for sample in edge_samples {
                edge_cases.push(sample.data.clone());
            }
        }

        edge_cases
    }

    pub fn create_test_entry_with_transactions(tx_count: usize) -> Vec<u8> {
        let mut entry_bytes = Vec::new();

        entry_bytes.extend_from_slice(&1u64.to_le_bytes());
        entry_bytes.extend_from_slice(get_random_hash_bytes());
        entry_bytes.extend_from_slice(&(tx_count as u32).to_le_bytes());

        entry_bytes
    }

    pub fn create_malformed_data_samples() -> Vec<Vec<u8>> {
        vec![
            vec![],
            vec![0],
            vec![255],
            vec![1, 2, 3],
            vec![0; 100],
            vec![255; 100],
            get_known_good_entry_bytes()[..10].to_vec(),
            get_known_good_transaction_bytes()[..20].to_vec(),
        ]
    }
}
